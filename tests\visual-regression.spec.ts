import { test, expect } from '@playwright/test';

test.describe('Visual Regression Tests', () => {
  test('homepage visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    // Wait for animations to complete
    await page.waitForTimeout(2000);
    
    // Take screenshot and compare with baseline
    await expect(page).toHaveScreenshot('homepage.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('services page visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/services');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('services.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('portfolio page visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/portfolio');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('portfolio.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('blog page visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/blog');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('blog.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('about page visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/about');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('about.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('contact page visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/contact');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('contact.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('mobile homepage visual regression', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('homepage-mobile.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('tablet portfolio visual regression', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('http://localhost:3000/portfolio');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('portfolio-tablet.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });

  test('services pricing cards visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/services');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Focus on the pricing cards section
    const pricingSection = page.locator('section').filter({ hasText: 'Mixing' });
    await expect(pricingSection).toHaveScreenshot('pricing-cards.png', {
      animations: 'disabled',
    });
  });

  test('portfolio filter interaction visual regression', async ({ page }) => {
    await page.goto('http://localhost:3000/portfolio');
    await page.waitForLoadState('networkidle');
    
    // Click on Mixing filter
    await page.getByRole('button', { name: 'Mixing' }).click();
    await page.waitForTimeout(1000);
    
    await expect(page).toHaveScreenshot('portfolio-mixing-filter.png', {
      fullPage: true,
      animations: 'disabled',
    });
  });
});
