"use client";

import { useRef, useMemo } from "react";
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from "@react-three/fiber";
import { Vector2, ShaderMaterial, Mesh, PlaneGeometry } from "three";
import { useMousePosition } from "@/hooks/use-mouse-position";

const vertexShader = `
  varying vec2 vUv;
  void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const fragmentShader = `
  uniform float uTime;
  uniform vec2 uMouse;
  varying vec2 vUv;

  void main() {
    vec2 uv = vUv;

    // Create wave effect
    float waves = 0.0;
    for(float i = 1.0; i <= 3.0; i++) {
      waves += sin(uv.x * 3.14 * i + uTime * 0.5) * 0.5 / i;
      waves += cos(uv.y * 3.14 * i + uTime * 0.5) * 0.5 / i;
    }

    // Mouse interaction
    float dist = length(uv - uMouse);
    waves += sin(dist * 10.0 - uTime) * 0.1 * (1.0 - smoothstep(0.0, 0.5, dist));

    // Color palette
    vec3 color1 = vec3(0.1, 0.1, 0.15); // Dark base
    vec3 color2 = vec3(0.95, 0.4, 0.1); // Orange accent

    vec3 finalColor = mix(color1, color2, waves * 0.3 + 0.2);
    float alpha = 0.8; // Slightly transparent to blend with background

    gl_FragColor = vec4(finalColor, alpha);
  }
`;

function WaveformMesh() {
  const meshRef = useRef<Mesh>(null);
  const materialRef = useRef<ShaderMaterial>(null);
  const mousePos = useMousePosition();
  const { viewport } = useThree();

  const uniforms = useMemo(
    () => ({
      uTime: { value: 0 },
      uMouse: { value: new Vector2(0.5, 0.5) },
    }),
    []
  );

  useFrame((state) => {
    if (materialRef.current) {
      materialRef.current.uniforms.uTime.value = state.clock.getElapsedTime();
      materialRef.current.uniforms.uMouse.value.set(mousePos.x, mousePos.y);
    }
  });

  const geometry = useMemo(() => {
    const geo = new PlaneGeometry(viewport.width, viewport.height, 32, 32);
    return geo;
  }, [viewport.width, viewport.height]);

  return (
    <mesh ref={meshRef} scale={[1, 1, 1]}>
      <primitive object={geometry} attach="geometry" />
      <shaderMaterial
        ref={materialRef}
        vertexShader={vertexShader}
        fragmentShader={fragmentShader}
        uniforms={uniforms}
        transparent={true}
        depthWrite={false}
      />
    </mesh>
  );
}

export function WaveformBackground() {
  return (
    <div className="fixed inset-0 -z-10">
      <Canvas>
        <WaveformMesh />
      </Canvas>
    </div>
  );
}
