import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info, AlertTriangle, AlertCircle, CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";

export type CalloutType = "info" | "warning" | "error" | "success";

export interface MdxCalloutProps {
  type?: CalloutType;
  title?: string;
  children: React.ReactNode;
  className?: string;
}

const icons = {
  info: Info,
  warning: AlertTriangle,
  error: AlertCircle,
  success: CheckCircle2,
} as const;

const styles = {
  info: {
    container:
      "border-blue-200 dark:border-blue-800 bg-gradient-to-r from-blue-50 to-blue-50/50 dark:from-blue-950/50 dark:to-blue-950/20 shadow-sm",
    icon: "text-blue-600 dark:text-blue-400",
    title: "text-blue-800 dark:text-blue-300 font-semibold",
    content: "text-blue-700 dark:text-blue-200",
  },
  warning: {
    container:
      "border-yellow-200 dark:border-yellow-800 bg-gradient-to-r from-yellow-50 to-yellow-50/50 dark:from-yellow-950/50 dark:to-yellow-950/20 shadow-sm",
    icon: "text-yellow-600 dark:text-yellow-400",
    title: "text-yellow-800 dark:text-yellow-300 font-semibold",
    content: "text-yellow-700 dark:text-yellow-200",
  },
  error: {
    container:
      "border-red-200 dark:border-red-800 bg-gradient-to-r from-red-50 to-red-50/50 dark:from-red-950/50 dark:to-red-950/20 shadow-sm",
    icon: "text-red-600 dark:text-red-400",
    title: "text-red-800 dark:text-red-300 font-semibold",
    content: "text-red-700 dark:text-red-200",
  },
  success: {
    container:
      "border-green-200 dark:border-green-800 bg-gradient-to-r from-green-50 to-green-50/50 dark:from-green-950/50 dark:to-green-950/20 shadow-sm",
    icon: "text-green-600 dark:text-green-400",
    title: "text-green-800 dark:text-green-300 font-semibold",
    content: "text-green-700 dark:text-green-200",
  },
} as const;

export function MdxCallout({
  type = "info",
  title,
  children,
  className,
}: MdxCalloutProps) {
  const Icon = icons[type];
  const style = styles[type];

  return (
    <Alert
      className={cn("my-8 border-l-4 rounded-lg", style.container, className)}
    >
      <Icon className={cn("h-5 w-5 mt-0.5", style.icon)} />
      {title && (
        <AlertTitle className={cn("mb-2", style.title)}>{title}</AlertTitle>
      )}
      <AlertDescription className={cn("leading-relaxed", style.content)}>
        {children}
      </AlertDescription>
    </Alert>
  );
}

// Export Callout component for MDX
export const Callout = MdxCallout;
