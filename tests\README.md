# Noize Capital Testing Suite

This directory contains essential end-to-end tests for the Noize Capital application using Playwright with TypeScript.

## 🧪 Simplified Test Structure

### Essential Tests

- `essential.spec.ts` - Core functionality tests covering all main features:
  - Homepage loading and key elements
  - Navigation between pages
  - Services page pricing display
  - Blog content accessibility
  - Portfolio project display
  - About page team information
  - Mobile responsive design
  - Console error checking
  - Basic performance validation
  - Page accessibility
  - SEO elements
  - Contact form functionality

## 🚀 Running Tests

### Local Development

```bash
# Run essential tests
npm run test

# Run tests with UI mode (interactive)
npm run test:ui

# Run tests in headed mode (see browser)
npm run test:headed

# Debug tests
npm run test:debug

# View HTML report
npm run test:report
```

### Advanced Options

```bash
# Run tests on specific project
npx playwright test essential.spec.ts --project=chromium
npx playwright test essential.spec.ts --project=mobile

# Run with specific options
npx playwright test essential.spec.ts --headed --slowMo=1000
```

## 🔧 Configuration

### Simplified Playwright Config (`playwright.config.ts`)

- **Base URL:** `http://localhost:3000`
- **Browsers:** Chromium (desktop + mobile)
- **Reporters:** HTML, List
- **Timeouts:** 30s test timeout, 10s action timeout
- **Screenshots:** On failure
- **Videos:** On failure
- **Traces:** On retry

### Test Coverage

- ✅ **Core Functionality** - All main pages load correctly
- ✅ **Navigation** - Inter-page navigation works
- ✅ **Responsive Design** - Mobile viewport testing
- ✅ **Performance** - Basic load time validation
- ✅ **Console Errors** - JavaScript error detection
- ✅ **SEO Basics** - Meta tags and titles
- ✅ **Form Functionality** - Contact form accessibility

## 🏗️ CI/CD Integration

### Simplified GitHub Actions (`.github/workflows/tests.yml`)

- **Quality Checks:** ESLint, TypeScript, Build
- **Essential Tests:** Single Chromium-based test suite
- **Deployment:** Automatic deployment to production on main branch

**Benefits of simplified approach:**

- ⚡ Faster CI runs (5-10 minutes vs 20-30 minutes)
- 💰 Lower GitHub Actions costs
- 🔧 Easier maintenance and debugging
- 🎯 Focus on tests that matter for your use case

## 🐛 Debugging

### Debug Commands

```bash
# Run with debug mode
npm run test:debug

# Run with headed browser
npm run test:headed

# Generate test code
npx playwright codegen localhost:3000
```

### Common Issues

1. **WebGL Errors:** Filtered out as expected in headless mode
2. **Timing Issues:** Use `waitForLoadState('networkidle')` for dynamic content
3. **Selector Issues:** Use role-based selectors when possible

## 📈 What We Simplified

### Removed Complex Features

- ❌ Visual regression testing (brittle, high maintenance)
- ❌ Cross-browser testing (Firefox, Safari - focus on Chromium)
- ❌ Complex page object models (over-engineering for simple site)
- ❌ Extensive e2e user journeys (basic site doesn't need them)
- ❌ Performance testing with Lighthouse (overkill)
- ❌ Multiple GitHub workflow files (consolidated to one)

### Kept Essential Features

- ✅ Core functionality testing (pages load, navigation works)
- ✅ Mobile responsive testing (important for all sites)
- ✅ Basic performance validation (load time checks)
- ✅ Console error detection (catches real issues)
- ✅ Form functionality testing (contact form works)
- ✅ SEO basics (meta tags present)

This simplified approach gives you **80% of the value with 20% of the complexity**.
