@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Blog Prose Styling */
@layer components {
  .blog-prose {
    max-width: none;
    color: hsl(var(--foreground));
    line-height: 1.75;
  }

  .blog-prose h1,
  .blog-prose h2,
  .blog-prose h3,
  .blog-prose h4,
  .blog-prose h5,
  .blog-prose h6 {
    color: hsl(var(--foreground));
    font-weight: 600;
    line-height: 1.25;
    scroll-margin-top: 5rem;
  }

  .blog-prose h1 {
    font-size: 2.25rem;
    margin-top: 0;
    margin-bottom: 2rem;
  }

  .blog-prose h2 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid hsl(var(--border));
  }

  .blog-prose h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .blog-prose h4 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .blog-prose p {
    margin-top: 0;
    margin-bottom: 1.5rem;
    line-height: 1.75;
  }

  .blog-prose ul,
  .blog-prose ol {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    padding-left: 1.5rem;
  }

  .blog-prose li {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.75;
  }

  .blog-prose blockquote {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem 1.5rem;
    border-left: 4px solid hsl(var(--primary));
    background: hsl(var(--muted) / 0.5);
    border-radius: 0 0.5rem 0.5rem 0;
    font-style: italic;
  }

  .blog-prose code {
    background: hsl(var(--muted));
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
      "Liberation Mono", Menlo, monospace;
  }

  .blog-prose pre {
    background: hsl(var(--muted)) !important;
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    padding: 1rem;
    overflow-x: auto;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .blog-prose pre code {
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
  }

  .blog-prose strong {
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .blog-prose a {
    color: hsl(var(--primary));
    text-decoration: underline;
    text-underline-offset: 2px;
  }

  .blog-prose a:hover {
    color: hsl(var(--primary) / 0.8);
  }

  .blog-prose img {
    border-radius: 0.5rem;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .blog-prose table {
    width: 100%;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    border-collapse: collapse;
  }

  .blog-prose th,
  .blog-prose td {
    padding: 0.75rem;
    border: 1px solid hsl(var(--border));
    text-align: left;
  }

  .blog-prose th {
    background: hsl(var(--muted));
    font-weight: 600;
  }

  .blog-prose h2 {
    position: relative;
  }

  .blog-prose h2::before {
    content: "";
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1.5rem;
    background: hsl(var(--primary));
    border-radius: 2px;
    opacity: 0.7;
  }

  /* Enhanced code blocks */
  .blog-prose pre {
    position: relative;
    background: hsl(var(--muted)) !important;
    border: 1px solid hsl(var(--border));
  }

  .blog-prose pre code {
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
  }

  /* Reading progress styles */
  .reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: hsl(var(--muted));
    z-index: 50;
  }

  .reading-progress-bar {
    height: 100%;
    background: linear-gradient(
      90deg,
      hsl(var(--primary)),
      hsl(var(--primary) / 0.7)
    );
    transition: width 0.1s ease-out;
  }

  /* Section dividers */
  .section-divider {
    margin: 3rem 0;
    text-align: center;
    position: relative;
  }

  .section-divider::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--border)),
      transparent
    );
  }

  .section-divider-icon {
    background: hsl(var(--background));
    padding: 0 1rem;
    color: hsl(var(--muted-foreground));
  }
}
