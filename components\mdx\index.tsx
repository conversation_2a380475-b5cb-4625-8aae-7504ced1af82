import {
  MdxCallout,
  type MdxCalloutProps,
  type CalloutType,
} from "./MdxCallout";
import { MdxImage, type MdxImageProps, img } from "./MdxImage";
import { Mdx<PERSON><PERSON><PERSON> } from "./MdxRenderer";
import { CodeBlock, Pre } from "./CodeBlock";

// Export components
export { MdxCallout, MdxImage, MdxRenderer, CodeBlock, Pre, img };

// Export component props types
export type { MdxCalloutProps, MdxImageProps, CalloutType };

// Export default components mapping for MDX
export const mdxComponents = {
  img,
  pre: Pre,
  Image: MdxImage,
  Callout: MdxCallout,
  CodeBlock,
};
