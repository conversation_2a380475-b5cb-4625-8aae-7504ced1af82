"use client";

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useMediaQuery } from "@/hooks/use-media-query";

interface TableOfContentsProps {
  className?: string;
}

interface HeadingItem {
  id: string;
  text: string;
  level: number;
}

export function TableOfContents({ className }: TableOfContentsProps) {
  const [headings, setHeadings] = useState<HeadingItem[]>([]);
  const [activeId, setActiveId] = useState<string>("");
  const [isOpen, setIsOpen] = useState(true);
  const isMobile = useMediaQuery("(max-width: 768px)");

  useEffect(() => {
    const headingElements = document.querySelectorAll("h2, h3");
    const items: HeadingItem[] = Array.from(headingElements).map(
      (heading, index) => {
        // Generate a unique ID if none exists
        const text = heading.textContent || "";
        const level = parseInt(heading.tagName.substring(1));
        const baseId = text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, "-") // Replace non-alphanumeric chars with hyphens
          .replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
        const uniqueId = heading.id || `${baseId}-${level}-${index}`;

        // Ensure the heading has an ID for scroll targeting
        if (!heading.id) {
          heading.id = uniqueId;
        }

        return {
          id: uniqueId,
          text,
          level,
        };
      }
    );
    setHeadings(items);

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id);
          }
        });
      },
      { rootMargin: "0% 0% -80% 0%" }
    );

    headingElements.forEach((heading) => observer.observe(heading));

    return () => observer.disconnect();
  }, []);

  if (headings.length === 0) return null;

  return (
    <nav
      className={cn(
        "space-y-2 rounded-lg border bg-card/50 backdrop-blur-sm p-4 shadow-sm",
        className
      )}
    >
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between">
          <p className="font-semibold text-base sm:text-sm text-foreground">
            Table of Contents
          </p>
          {isMobile && (
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="w-9 p-0">
                <ChevronDown
                  className={cn("h-4 w-4 transition-transform", {
                    "transform rotate-180": isOpen,
                  })}
                />
              </Button>
            </CollapsibleTrigger>
          )}
        </div>
        <CollapsibleContent className="mt-4">
          <ul className="space-y-3 sm:space-y-2 text-base sm:text-sm">
            {headings.map((heading) => (
              <li
                key={heading.id}
                style={{ paddingLeft: `${(heading.level - 2) * 1}rem` }}
              >
                <a
                  href={`#${heading.id}`}
                  className={cn(
                    "text-muted-foreground hover:text-primary transition-all duration-200",
                    "hover:translate-x-1 hover:font-medium",
                    activeId === heading.id &&
                      "text-primary font-medium border-l-2 border-primary pl-2 -ml-2",
                    "block py-2 sm:py-1 rounded-sm"
                  )}
                  onClick={(e) => {
                    e.preventDefault();
                    document.getElementById(heading.id)?.scrollIntoView({
                      behavior: "smooth",
                    });
                  }}
                >
                  {heading.text}
                </a>
              </li>
            ))}
          </ul>
        </CollapsibleContent>
      </Collapsible>
    </nav>
  );
}
