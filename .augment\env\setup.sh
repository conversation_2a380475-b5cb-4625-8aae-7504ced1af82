#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js and npm are available
node --version
npm --version

# Navigate to the workspace directory
cd /mnt/persist/workspace

# Install Playwright and related dependencies with legacy peer deps to resolve conflicts
npm install --save-dev @playwright/test --legacy-peer-deps
npm install --save-dev playwright --legacy-peer-deps

# Initialize Playwright configuration and install browsers
npx playwright install

# Install system dependencies for Playwright browsers
npx playwright install-deps

# Create Playwright configuration file
cat > playwright.config.ts << 'EOF'
import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: 'html',
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
EOF

# Create tests directory
mkdir -p tests

# Create the updated test file that handles WebGL errors
cat > tests/example.spec.ts << 'EOF'
import { test, expect } from '@playwright/test';

test('has correct title', async ({ page }) => {
  await page.goto('/');

  // Expect the actual title of the Noize Capital application
  await expect(page).toHaveTitle(/Noize Capital/);
});

test('navigation test', async ({ page }) => {
  await page.goto('/');

  // Wait for the page to load
  await page.waitForLoadState('networkidle');

  // Check if the page loads successfully
  await expect(page).toHaveURL('http://localhost:3000/');
});

test('page loads without critical errors', async ({ page }) => {
  await page.goto('/');

  // Wait for the page to load completely
  await page.waitForLoadState('domcontentloaded');

  // Collect console errors, but filter out expected WebGL errors
  const logs: string[] = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      const text = msg.text();
      // Filter out expected WebGL errors that occur in headless environments
      if (!text.includes('WebGL') && !text.includes('THREE.WebGLRenderer')) {
        logs.push(text);
      }
    }
  });

  // Wait a bit to catch any console errors
  await page.waitForTimeout(1000);

  // Verify no critical console errors occurred (excluding WebGL errors)
  expect(logs).toHaveLength(0);
});

test('page content loads', async ({ page }) => {
  await page.goto('/');

  // Wait for the page to load
  await page.waitForLoadState('domcontentloaded');

  // Check that the page has some content (not just a blank page)
  const bodyText = await page.textContent('body');
  expect(bodyText).toBeTruthy();
  expect(bodyText!.length).toBeGreaterThan(0);
});
EOF

# Add test scripts to package.json
npm pkg set scripts.test:e2e="npx playwright test"
npm pkg set scripts.test:e2e:ui="npx playwright test --ui"
npm pkg set scripts.test:e2e:headed="npx playwright test --headed"

# Update .gitignore to exclude Playwright artifacts
if [ -f .gitignore ]; then
    if ! grep -q "# Playwright" .gitignore; then
        echo "" >> .gitignore
        echo "# Playwright" >> .gitignore
        echo "/test-results/" >> .gitignore
        echo "/playwright-report/" >> .gitignore
        echo "/blob-report/" >> .gitignore
        echo "/playwright/.cache/" >> .gitignore
    fi
else
    cat > .gitignore << 'EOF'
# Dependencies
node_modules/

# Next.js
.next/
out/

# Environment variables
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Playwright
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
EOF
fi

echo "Playwright setup completed successfully!"
echo "Tests are now configured for the Noize Capital application with WebGL error handling."
echo ""
echo "Available test commands:"
echo "  npm run test:e2e          - Run all tests"
echo "  npm run test:e2e:ui       - Run tests with UI mode"
echo "  npm run test:e2e:headed   - Run tests in headed mode"