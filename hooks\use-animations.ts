import { useState, useEffect } from "react";
import { Variants } from "framer-motion";
import { useIsMobile } from "./use-mobile";

export function useAnimations() {
  const [hasReducedMotion, setHasReducedMotion] = useState(false);
  const isMobile = useIsMobile();

  // Check for reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setHasReducedMotion(mediaQuery.matches);

    const onChange = () => setHasReducedMotion(mediaQuery.matches);
    mediaQuery.addEventListener("change", onChange);
    return () => mediaQuery.removeEventListener("change", onChange);
  }, []);

  // Determine if we should use simpler animations
  const useSimpleAnimations = hasReducedMotion || isMobile;

  // Shorter duration for mobile
  const animationDuration = useSimpleAnimations ? 0.3 : 0.5;

  // Smaller movement values for mobile
  const yOffset = isMobile ? 10 : 20;
  const xOffset = isMobile ? 10 : 20;

  // Common animation variants
  const fadeIn: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: useSimpleAnimations ? 0.2 : 0.5,
      },
    },
  };

  const fadeInUp: Variants = {
    hidden: { opacity: 0, y: yOffset },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: animationDuration,
      },
    },
  };

  const fadeInDown: Variants = {
    hidden: { opacity: 0, y: -yOffset },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: animationDuration,
      },
    },
  };

  const fadeInLeft: Variants = {
    hidden: { opacity: 0, x: -xOffset },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: animationDuration,
      },
    },
  };

  const fadeInRight: Variants = {
    hidden: { opacity: 0, x: xOffset },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: animationDuration,
      },
    },
  };

  const staggerChildren: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: useSimpleAnimations ? 0.05 : 0.1,
      },
    },
  };

  const scaleIn: Variants = {
    hidden: { opacity: 0, scale: useSimpleAnimations ? 0.95 : 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: animationDuration,
      },
    },
  };

  // Card hover animations - simpler for mobile
  const cardHover = useSimpleAnimations
    ? {
        scale: isMobile ? 1.01 : 1.02,
        transition: { duration: 0.2 },
      }
    : {
        scale: 1.03,
        y: -5,
        transition: { duration: 0.3 },
      };

  return {
    hasReducedMotion,
    fadeIn,
    fadeInUp,
    fadeInDown,
    fadeInLeft,
    fadeInRight,
    staggerChildren,
    scaleIn,
    cardHover,
  };
}
