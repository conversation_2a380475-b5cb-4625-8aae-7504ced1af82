"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";

interface SpotifyEmbedProps {
  spotifyId: string;
  type?: "track" | "album" | "playlist";
  className?: string;
  height?: number;
}

export function SpotifyEmbed({
  spotifyId,
  type = "track",
  className,
  height = 80,
}: SpotifyEmbedProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Construct the Spotify embed URL
  const embedUrl = `https://open.spotify.com/embed/${type}/${spotifyId}`;

  // Handle iframe load event
  const handleLoad = () => {
    setIsLoaded(true);
  };

  // Handle iframe error event
  const handleError = () => {
    setHasError(true);
  };

  return (
    <div className={cn("relative rounded-md overflow-hidden", className)}>
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="animate-pulse text-orange-500">Loading...</div>
        </div>
      )}

      {hasError && (
        <div className="flex items-center justify-center h-20 bg-black/50 backdrop-blur-sm border border-white/10 rounded-md">
          <p className="text-sm text-gray-400">
            Failed to load Spotify track. Please check the ID or try again
            later.
          </p>
        </div>
      )}

      <iframe
        src={embedUrl}
        width="100%"
        height={height}
        frameBorder="0"
        allow="autoplay; clipboard-write; encrypted-media; fullscreen; picture-in-picture"
        loading="lazy"
        onLoad={handleLoad}
        onError={handleError}
        className={cn(
          "border-0 rounded-md",
          !isLoaded && "opacity-0",
          hasError && "hidden"
        )}
      ></iframe>
    </div>
  );
}
