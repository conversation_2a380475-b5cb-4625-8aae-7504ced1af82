name: PR Preview & Tests

on:
  pull_request:
    branches: [ main, dev ]
    types: [opened, synchronize, reopened]

jobs:
  # Deploy preview
  preview:
    name: Deploy Preview
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel Preview
      uses: amondnet/vercel-action@v25
      id: vercel-deploy
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
    
    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🚀 Preview Deployment
            
            Your changes have been deployed to a preview environment:
            
            **Preview URL:** ${{ steps.vercel-deploy.outputs.preview-url }}
            
            The preview will be updated automatically when you push new commits to this PR.`
          })

  # Test against preview
  test-preview:
    name: Test Preview Deployment
    runs-on: ubuntu-latest
    needs: preview
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Wait for preview deployment
      run: sleep 30
    
    - name: Test preview deployment
      run: |
        # Update playwright config to test against preview URL
        export PREVIEW_URL="${{ needs.preview.outputs.preview-url }}"
        npx playwright test tests/demo-working.spec.ts --project=chromium
      env:
        PREVIEW_URL: ${{ needs.preview.outputs.preview-url }}
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: preview-test-results
        path: |
          playwright-report/
          test-results/
        retention-days: 7

  # Visual regression testing
  visual-tests:
    name: Visual Regression Tests
    runs-on: ubuntu-latest
    needs: preview
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Run visual regression tests
      run: |
        # Create a simple visual test
        npx playwright test --project=chromium --grep="visual"
      continue-on-error: true
    
    - name: Upload visual test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: visual-test-results
        path: |
          test-results/
          playwright-report/
        retention-days: 7

  # Accessibility testing
  accessibility:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    needs: preview
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 'lts/*'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci --legacy-peer-deps
    
    - name: Install axe-core
      run: npm install -g @axe-core/cli
    
    - name: Wait for preview deployment
      run: sleep 30
    
    - name: Run accessibility tests
      run: |
        axe ${{ needs.preview.outputs.preview-url }} --exit
        axe ${{ needs.preview.outputs.preview-url }}/services --exit
        axe ${{ needs.preview.outputs.preview-url }}/portfolio --exit
        axe ${{ needs.preview.outputs.preview-url }}/blog --exit
        axe ${{ needs.preview.outputs.preview-url }}/about --exit
        axe ${{ needs.preview.outputs.preview-url }}/contact --exit
      continue-on-error: true

  # Comment with test results
  comment-results:
    name: Comment Test Results
    runs-on: ubuntu-latest
    needs: [test-preview, visual-tests, accessibility]
    if: always()
    
    steps:
    - name: Comment PR with test results
      uses: actions/github-script@v7
      with:
        script: |
          const testResults = {
            preview: '${{ needs.test-preview.result }}',
            visual: '${{ needs.visual-tests.result }}',
            accessibility: '${{ needs.accessibility.result }}'
          };
          
          const getEmoji = (result) => {
            switch(result) {
              case 'success': return '✅';
              case 'failure': return '❌';
              case 'cancelled': return '⏹️';
              default: return '⚠️';
            }
          };
          
          const body = `## 🧪 Test Results
          
          | Test Type | Status |
          |-----------|--------|
          | Preview Tests | ${getEmoji(testResults.preview)} ${testResults.preview} |
          | Visual Regression | ${getEmoji(testResults.visual)} ${testResults.visual} |
          | Accessibility | ${getEmoji(testResults.accessibility)} ${testResults.accessibility} |
          
          ${testResults.preview === 'failure' ? '⚠️ Some tests failed. Please check the logs for details.' : ''}
          ${testResults.visual === 'failure' ? '⚠️ Visual regression detected. Please review the changes.' : ''}
          ${testResults.accessibility === 'failure' ? '⚠️ Accessibility issues found. Please address them before merging.' : ''}
          `;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: body
          })
