import { Suspense } from 'react';
import { Metadata } from 'next';
import { getPosts, getAllTags } from '@/lib/blog';
import { PostFrontmatter } from '@/lib/blog';
import { PostCard } from '@/components/blog/PostCard';
import { TagsFilter } from '@/components/blog/TagsFilter';

export const dynamic = 'force-static';

export const metadata: Metadata = {
  title: 'Blog | Noize Capital',
  description: 'Articles and guides about music production, mixing, and mastering',
};

interface PageSearchParams {
  tag?: string;
}

export default async function BlogPage({
  searchParams
}: {
  searchParams: Promise<PageSearchParams>;
}) {
  const resolvedParams = await searchParams as unknown as PageSearchParams;
  const posts = await getPosts();
  const allTags = getAllTags(posts);
  
  const filteredPosts = resolvedParams.tag
    ? posts.filter(post =>
        post.frontmatter.tags.includes(resolvedParams.tag as PostFrontmatter['tags'][number])
      )
    : posts;

  return (
    <main className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
      <div className="space-y-6 sm:space-y-8">
        <div className="text-center space-y-3 sm:space-y-4">
          <h1 className="text-3xl sm:text-4xl font-bold">Blog</h1>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto px-2">
            Insights, tutorials, and guides about music production, mixing, and mastering
          </p>
        </div>

        <Suspense fallback={<div>Loading tags...</div>}>
          <TagsFilter
            tags={allTags}
            selectedTag={resolvedParams.tag || null}
          />
        </Suspense>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {filteredPosts.map((post) => (
            <Suspense key={post.slug} fallback={<div>Loading post...</div>}>
              <PostCard post={post} />
            </Suspense>
          ))}
          {filteredPosts.length === 0 && (
            <p className="col-span-full text-center text-muted-foreground py-6 sm:py-8">
              No posts found {resolvedParams.tag && `for tag "${resolvedParams.tag}"`}
            </p>
          )}
        </div>
      </div>
    </main>
  );
}