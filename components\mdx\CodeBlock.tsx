"use client";

import { useState } from 'react';
import { Check, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CodeBlockProps {
  children: React.ReactNode;
  className?: string;
  language?: string;
  title?: string;
}

export function CodeBlock({ children, className, language, title }: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    const codeElement = document.querySelector(`pre.${className} code`);
    if (codeElement) {
      const text = codeElement.textContent || '';
      try {
        await navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error('Failed to copy text: ', err);
      }
    }
  };

  return (
    <div className="relative group my-6">
      {title && (
        <div className="flex items-center justify-between bg-muted border border-b-0 rounded-t-lg px-4 py-2">
          <span className="text-sm font-medium text-muted-foreground">
            {title}
          </span>
          {language && (
            <span className="text-xs text-muted-foreground uppercase">
              {language}
            </span>
          )}
        </div>
      )}
      <div className="relative">
        <pre className={cn(
          "relative overflow-x-auto",
          title ? "rounded-t-none" : "rounded-lg",
          className
        )}>
          {children}
        </pre>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "absolute top-2 right-2 h-8 w-8 p-0",
            "opacity-0 group-hover:opacity-100 transition-opacity",
            "bg-background/80 hover:bg-background border"
          )}
          onClick={copyToClipboard}
        >
          {copied ? (
            <Check className="h-3 w-3 text-green-500" />
          ) : (
            <Copy className="h-3 w-3" />
          )}
        </Button>
      </div>
    </div>
  );
}

// Enhanced pre component for MDX
export function Pre({ children, ...props }: React.HTMLAttributes<HTMLPreElement>) {
  return (
    <CodeBlock {...props}>
      {children}
    </CodeBlock>
  );
}
