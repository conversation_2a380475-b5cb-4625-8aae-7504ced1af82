---
title: "The Track Prep Checklist That Saved Me $2,000 (And Will Save Your Mix)"
date: "2025-05-03"
tags: ["Mixing", "Client Guides"]
excerpt: "I used to send tracks to mixing engineers and get back mediocre results. Then I learned this preparation process that transformed my mixes—and cut my revision costs to zero."
featureImage: "track-prep.jpg"
published: true
---

# The Track Prep Checklist That Saved Me $2,000 (And Will Save Your Mix)

Three years ago, I sent what I thought was a "finished" track to a top mixing engineer. $800 later, I got back a mix that sounded... fine. Not great, not terrible, just fine.

The problem wasn't the engineer—it was me. I had sent him a mess of poorly organized, badly recorded, and technically flawed stems. He spent most of his time fixing my mistakes instead of making my music shine.

That expensive lesson taught me something crucial: **The quality of your mix is determined before the mixing engineer even touches your track.**

Since then, I've developed a track preparation process that has:
- Eliminated mix revisions (saving me thousands in revision fees)
- Reduced mixing time by 40% (meaning lower costs)
- Consistently delivered mixes that sound radio-ready on the first pass

<MdxCallout type="success" title="The Real Cost of Poor Prep">
  A professional mixing engineer charges $100-200/hour. If they spend 2 hours fixing your prep mistakes instead of mixing, that's $400 wasted. This checklist pays for itself on the first track.
</MdxCallout>

## The $800 Mistake: What I Did Wrong

Let me tell you exactly what I sent to that mixing engineer:

- 47 audio files with names like "Audio_01.wav" and "Untitled_23.wav"
- Vocals recorded at different levels across 12 different takes
- A bass track that was 90% DI and 10% amp (I forgot to mention this)
- Drum samples at different sample rates
- Guitar tracks with effects printed that I later decided I didn't like
- No reference tracks
- No notes about my vision

The engineer did his best, but he had to guess what I wanted. The result was technically competent but creatively flat.

**Here's the preparation system that fixed everything:**

<SectionDivider />

## Step 1: The "Radio Test" - Is Your Track Actually Ready?

Before you even think about mixing, your track needs to pass the radio test. Here's what I mean:

**Play your rough mix on:**
1. Car speakers at highway volume
2. Phone speakers
3. Cheap earbuds
4. Your worst computer speakers

**If any of these are true, STOP and fix them first:**
- You can't hear the vocals clearly
- The bass disappears completely
- Any element sounds distorted or harsh
- The track feels unfinished or has obvious gaps

<MdxCallout type="warning" title="The Harsh Truth">
  If your track doesn't work on terrible speakers, no amount of professional mixing will save it. Fix the arrangement and recording first.
</MdxCallout>

**Real Example:** I once spent $600 mixing a track where the chorus vocal was recorded 6dB quieter than the verse. The engineer did his best, but it never sounded right. I should have re-recorded it.

<SectionDivider />

## Step 2: The "Stem Surgery" - Preparing Your Files Like a Pro

### The Naming System That Changed Everything

Stop naming tracks "Audio_01.wav." Here's my system:

```txt
Format: [INSTRUMENT]_[PART]_[TAKE]_[NOTES].wav

Examples:
VOX_Lead_Verse_Comp.wav
VOX_Lead_Chorus_Comp.wav
VOX_Harmony_Chorus_Stack.wav
GTR_Electric_Main_Riff.wav
GTR_Electric_Solo_Take3.wav
DR_Kick_Sample.wav
DR_Snare_Sample.wav
BASS_DI_Main.wav
KEYS_Piano_Verse.wav
```

**Why this works:** Your mixing engineer instantly knows what everything is, where it goes, and how it fits together.

### The "Consolidation Rule" - One Track, One File

**Before:** 12 vocal takes spread across the timeline
**After:** One comped vocal file with the best parts

**Before:** Drum kit with 47 individual hits
**After:** Consolidated drum stems (kick, snare, hi-hats, overheads, room)

**Before:** Guitar with 6 different amp settings
**After:** One main guitar track, one double track

<MdxCallout type="info" title="The Consolidation Sweet Spot">
  Aim for 12-20 stems maximum. If you have more than 25 tracks, you're probably not being decisive enough about what's actually important.
</MdxCallout>

### The Technical Specs That Actually Matter

```txt
Export Settings (Non-Negotiable):
- Format: WAV (never MP3, never AIFF)
- Sample Rate: Match your project (44.1kHz or 48kHz)
- Bit Depth: 24-bit minimum
- Start Time: All files start at 00:00:00
- Length: All files same length (include the silence)
```

**Why the same length matters:** If your verse vocal is 30 seconds but your chorus vocal is 45 seconds, the mixing engineer has to guess where they line up. Make all files the same length as your full song.

<SectionDivider />

## Step 3: The "Level Police" - Getting Your Gain Staging Right

This is where most people screw up. Here's the truth about levels:

### The Magic Numbers

```txt
Target Levels for Mixing:
Peak Levels: -12dB to -6dB (never higher)
Average Levels: -18dB to -12dB
Headroom: At least 6dB on every track
```

**Translation:** Your loudest parts should hit around -6dB, your average level should be around -15dB, and you should NEVER see red on any meter.

### The "Clipping Check" Protocol

Before exporting anything:
1. Solo each track
2. Play the loudest section
3. If the meter goes above -6dB, turn it down
4. If you see any red, start over

<MdxCallout type="error" title="Clipping = Game Over">
  Digital clipping cannot be fixed. If your tracks are clipped, you need to re-record or re-export them. No mixing engineer can fix digital distortion.
</MdxCallout>

## Step 4: The "Communication Package" - Tell Your Story

This is the secret sauce that separates good mixes from great ones. Your mixing engineer needs to understand your vision.

### The Reference Track Strategy

**Don't just send one reference track—send three:**

1. **Overall Vibe Reference:** "I want it to feel like this song"
2. **Vocal Reference:** "I want the vocals to sound like this"
3. **Sonic Reference:** "I want the overall sound to be like this"

**Example Package:**
- Overall Vibe: "Blinding Lights" by The Weeknd (retro energy)
- Vocal Reference: "Someone You Loved" by Lewis Capaldi (intimate, present)
- Sonic Reference: "Watermelon Sugar" by Harry Styles (warm, punchy)

### The "Vision Document" Template

```txt
TRACK: [Song Title]
TEMPO: [BPM]
KEY: [Key signature]
GENRE: [Primary genre]

VISION:
"This song should feel like [emotion/mood]. The vocals need to be [adjective],
the drums should [description], and the overall vibe is [description]."

REFERENCES:
1. Overall: [Artist - Song]
2. Vocals: [Artist - Song]
3. Sound: [Artist - Song]

SPECIAL NOTES:
- Any specific requests
- Problem areas you're aware of
- Creative ideas you want to try
```

<MdxCallout type="success" title="The Magic of Specificity">
  Instead of "make it sound good," try "I want the vocals to cut through like Adele but with the warmth of John Mayer." Specific references give your engineer a clear target.
</MdxCallout>

<SectionDivider />

## Step 5: The "Final Check" Protocol

Before you hit send, run through this checklist:

### The Technical Checklist

```txt
✓ All files are WAV, 24-bit, same sample rate
✓ All files start at 00:00:00 and are the same length
✓ File names follow the naming convention
✓ No files peak above -6dB
✓ No clipping anywhere
✓ All tracks are consolidated/comped
✓ Unused tracks removed
✓ 12-20 stems maximum
```

### The Creative Checklist

```txt
✓ Reference tracks selected and included
✓ Vision document written
✓ Tempo and key documented
✓ Any special requests noted
✓ Problem areas identified
✓ Rough mix included for context
```

### The "Sanity Check" Test

Import all your stems into a fresh project and:
1. Line them up at 00:00:00
2. Play them together
3. Does it sound like your song?
4. Are all the parts there?
5. Do the levels make sense?

If anything sounds wrong, fix it before sending.

<SectionDivider />

## The Results: What This Process Actually Gets You

Since implementing this system:

**Before:** 3-4 revision rounds per mix, $200-400 in revision fees
**After:** 95% of mixes approved on first pass, zero revision fees

**Before:** 2-3 weeks turnaround time
**After:** 3-5 days turnaround time

**Before:** Mixes that sounded "fine"
**After:** Mixes that sound radio-ready

**The secret:** When you remove all the guesswork and technical problems, your mixing engineer can focus 100% on making your music sound incredible.

<MdxCallout type="warning" title="The Investment Mindset">
  Yes, this preparation takes time. But spending 2 hours on prep saves you $400 in revisions and gets you a better mix. That's a $200/hour return on your time investment.
</MdxCallout>

## Your Next Steps

1. **Download this checklist** and use it for your next track
2. **Start with one song** - don't try to prep your entire album at once
3. **Time yourself** - see how long proper prep actually takes (hint: less than you think)
4. **Compare the results** - A/B test a properly prepped mix against your old method

The difference will be immediately obvious.

**Ready to transform your mixes?** Start with your next track and follow this exact process. Your mixing engineer (and your wallet) will thank you.
