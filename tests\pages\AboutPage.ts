import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class AboutPage extends BasePage {
  readonly pageTitle: Locator;
  readonly aboutContent: Locator;
  readonly teamSection: Locator;
  readonly missionStatement: Locator;

  constructor(page: Page) {
    super(page);
    this.pageTitle = page.getByRole('heading', { name: /about/i });
    this.aboutContent = page.locator('[data-testid="about-content"]');
    this.teamSection = page.locator('[data-testid="team-section"]');
    this.missionStatement = page.locator('[data-testid="mission-statement"]');
  }

  async goto() {
    await super.goto('/about');
  }

  async expectPageTitle() {
    await expect(this.pageTitle).toBeVisible();
  }

  async expectAboutContent() {
    await expect(this.aboutContent).toBeVisible();
  }

  async expectMissionStatement() {
    // Check for mission statement or company description
    await expect(this.page.getByText(/precision sound/i)).toBeVisible();
    await expect(this.page.getByText(/independent spirit/i)).toBeVisible();
  }

  async expectTeamInformation() {
    // Check for team member information if it exists
    const teamMembers = this.page.locator('[data-testid="team-member"]');
    const count = await teamMembers.count();
    
    if (count > 0) {
      await expect(teamMembers.first()).toBeVisible();
    }
  }

  async expectCompanyValues() {
    // Check for company values or principles
    await expect(this.page.getByText(/quality/i)).toBeVisible();
  }

  async expectResponsiveLayout() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // On mobile, content should be stacked vertically
      await this.expectAboutContent();
    } else {
      // On desktop, content might be in a specific layout
      await this.expectAboutContent();
    }
  }
}
