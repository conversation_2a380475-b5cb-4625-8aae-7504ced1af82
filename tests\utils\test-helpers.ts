import { Page, expect } from '@playwright/test';

export class TestHelpers {
  static async waitForAnimations(page: Page, timeout: number = 1000) {
    // Wait for CSS animations and transitions to complete
    await page.waitForTimeout(timeout);
  }

  static async checkAccessibility(page: Page) {
    // Basic accessibility checks
    
    // Check for alt text on images
    const images = page.locator('img');
    const imageCount = await images.count();
    
    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i);
      const alt = await img.getAttribute('alt');
      expect(alt).toBeTruthy();
    }
    
    // Check for proper heading hierarchy
    const h1Count = await page.locator('h1').count();
    expect(h1Count).toBeLessThanOrEqual(1); // Should have at most one h1
    
    // Check for form labels
    const inputs = page.locator('input[type="text"], input[type="email"], textarea');
    const inputCount = await inputs.count();
    
    for (let i = 0; i < inputCount; i++) {
      const input = inputs.nth(i);
      const id = await input.getAttribute('id');
      const ariaLabel = await input.getAttribute('aria-label');
      const ariaLabelledBy = await input.getAttribute('aria-labelledby');
      
      if (id) {
        const label = page.locator(`label[for="${id}"]`);
        const labelExists = await label.count() > 0;
        expect(labelExists || ariaLabel || ariaLabelledBy).toBeTruthy();
      }
    }
  }

  static async checkPerformance(page: Page) {
    // Basic performance checks
    const navigationTiming = await page.evaluate(() => {
      const timing = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: timing.domContentLoadedEventEnd - timing.domContentLoadedEventStart,
        loadComplete: timing.loadEventEnd - timing.loadEventStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
      };
    });

    // Assert reasonable performance metrics
    expect(navigationTiming.domContentLoaded).toBeLessThan(3000); // 3 seconds
    expect(navigationTiming.firstContentfulPaint).toBeLessThan(2000); // 2 seconds
  }

  static async checkSEO(page: Page) {
    // Basic SEO checks
    
    // Check for title tag
    const title = await page.title();
    expect(title).toBeTruthy();
    expect(title.length).toBeGreaterThan(10);
    expect(title.length).toBeLessThan(60);
    
    // Check for meta description
    const metaDescription = await page.locator('meta[name="description"]').getAttribute('content');
    if (metaDescription) {
      expect(metaDescription.length).toBeGreaterThan(50);
      expect(metaDescription.length).toBeLessThan(160);
    }
    
    // Check for canonical URL
    const canonical = await page.locator('link[rel="canonical"]').getAttribute('href');
    if (canonical) {
      expect(canonical).toMatch(/^https?:\/\//);
    }
  }

  static async checkResponsiveDesign(page: Page) {
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1024, height: 768, name: 'Desktop Small' },
      { width: 1920, height: 1080, name: 'Desktop Large' },
    ];

    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      // Check that content is visible and not overflowing
      const body = page.locator('body');
      const bodyBox = await body.boundingBox();
      
      if (bodyBox) {
        expect(bodyBox.width).toBeLessThanOrEqual(viewport.width + 20); // Allow small margin
      }
      
      // Check that navigation is accessible
      const nav = page.locator('nav');
      await expect(nav).toBeVisible();
    }
  }

  static async simulateSlowNetwork(page: Page) {
    // Simulate slow 3G network
    const client = await page.context().newCDPSession(page);
    await client.send('Network.emulateNetworkConditions', {
      offline: false,
      downloadThroughput: 500 * 1024 / 8, // 500kb/s
      uploadThroughput: 500 * 1024 / 8,
      latency: 400,
    });
  }

  static async checkConsoleErrors(page: Page, allowedErrors: string[] = []) {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        
        // Filter out allowed errors and common false positives
        const isAllowed = allowedErrors.some(allowed => text.includes(allowed));
        const isWebGLError = text.includes('WebGL') || text.includes('THREE.WebGLRenderer');
        const isCanvasError = text.includes('canvas.getContext');
        
        if (!isAllowed && !isWebGLError && !isCanvasError) {
          errors.push(text);
        }
      }
    });

    await page.waitForTimeout(2000);
    
    if (errors.length > 0) {
      console.warn('Console errors found:', errors);
    }
    
    return errors;
  }

  static async takeFullPageScreenshot(page: Page, name: string) {
    await page.screenshot({
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true,
    });
  }

  static async scrollToElement(page: Page, selector: string) {
    await page.locator(selector).scrollIntoViewIfNeeded();
    await page.waitForTimeout(500);
  }

  static async waitForNetworkIdle(page: Page, timeout: number = 5000) {
    await page.waitForLoadState('networkidle', { timeout });
  }

  static generateTestData() {
    return {
      user: {
        name: 'Test User',
        email: '<EMAIL>',
        message: 'This is a test message for automated testing purposes.',
      },
      invalidEmail: 'invalid-email-format',
      longMessage: 'A'.repeat(1000), // Very long message for testing
    };
  }
}
