import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { notFound } from "next/navigation";
import Image from "next/image";
import { getPostBySlug, getPostSlugs, type Post } from "@/lib/blog";
import { TableOfContents } from "@/components/blog/TableOfContents";
import { ReadingProgress } from "@/components/blog/ReadingProgress";
import { Badge } from "@/components/ui/badge";
import { MdxRenderer } from "@/components/mdx/MdxRenderer";
import { Skeleton } from "@/components/ui/skeleton";

// Force page to be statically generated
export const dynamic = "force-static";
export const revalidate = 3600; // Revalidate every hour

// Generate static paths at build time
export async function generateStaticParams() {
  const slugs = await getPostSlugs();
  return slugs.map((slug) => ({ slug }));
}

// Metadata generation with better error handling
export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  try {
    const resolvedParams = await params;
    const post = await getPostBySlug(resolvedParams.slug);

    if (!post) {
      return {
        title: "Post Not Found",
        description: "The requested blog post could not be found.",
        robots: { index: false },
      };
    }

    return {
      title: post.frontmatter.title,
      description: post.frontmatter.excerpt,
      keywords: post.frontmatter.tags,
      authors: [{ name: "Noize Capital" }],
      openGraph: {
        title: post.frontmatter.title,
        description: post.frontmatter.excerpt,
        type: "article",
        publishedTime: post.frontmatter.date,
        ...(post.frontmatter.featureImage && {
          images: [
            {
              url: post.frontmatter.featureImage,
              width: 1200,
              height: 630,
              alt: post.frontmatter.title,
            },
          ],
        }),
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Error",
      description: "An error occurred while loading the blog post.",
      robots: { index: false },
    };
  }
}

type BlogPostPageProps = {
  params: Promise<{ slug: string }>;
  searchParams?: Promise<{ [key: string]: string | string[] | undefined }>;
};

// Loading component
function PostSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <Skeleton className="h-12 w-3/4 mx-auto" />
        <Skeleton className="h-4 w-48 mx-auto" />
      </div>
      <Skeleton className="h-[400px] w-full rounded-lg" />
      <div className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-4/6" />
      </div>
    </div>
  );
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  let post: Post | null = null;

  try {
    const resolvedParams = await params;
    post = await getPostBySlug(resolvedParams.slug);

    if (!post) {
      notFound();
    }
  } catch (error) {
    console.error("Error loading blog post:", error);
    throw new Error("Failed to load blog post");
  }

  return (
    <>
      <ReadingProgress />
      <main className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <article className="max-w-4xl mx-auto">
          <div className="space-y-3 sm:space-y-4 text-center mb-6 sm:mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold tracking-tight">
              {post.frontmatter.title}
            </h1>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4 text-muted-foreground">
              <time dateTime={post.frontmatter.date}>
                {new Date(post.frontmatter.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                })}
              </time>
              <div className="flex gap-2">
                {post.frontmatter.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {post.frontmatter.featureImage && (
            <div className="relative w-full h-[250px] sm:h-[400px] mb-6 sm:mb-8">
              <Image
                src={post.frontmatter.featureImage}
                alt={post.frontmatter.title}
                fill
                className="object-cover rounded-lg"
                priority
              />
            </div>
          )}

          {/* Show ToC at top on mobile */}
          <div className="block lg:hidden mb-6">
            <TableOfContents />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-[1fr_280px] gap-6 sm:gap-8">
            <div className="min-w-0">
              <Suspense fallback={<PostSkeleton />}>
                <MdxRenderer code={post.code} />
              </Suspense>
            </div>

            <aside className="hidden lg:block border-l pl-6">
              <div className="sticky top-8">
                <TableOfContents />
              </div>
            </aside>
          </div>
        </article>
      </main>
    </>
  );
}
