import Image from 'next/image';
import type { ImageProps } from 'next/image';

export interface MdxImageProps extends Omit<ImageProps, 'src'> {
  src: string;
  alt: string;
  caption?: string;
}

export function MdxImage({ src, alt, caption, ...props }: MdxImageProps) {
  // Handle both relative and absolute paths for blog images
  const imageSrc = src.startsWith('http') ? src : `/blog/${src}`;

  return (
    <figure className="my-8">
      <div className="overflow-hidden rounded-lg">
        <Image
          src={imageSrc}
          alt={alt}
          width={720}
          height={480}
          className="object-cover w-full"
          quality={90}
          {...props}
        />
      </div>
      {caption && (
        <figcaption className="mt-2 text-center text-sm text-muted-foreground">
          {caption}
        </figcaption>
      )}
    </figure>
  );
}

// Create a properly typed img component for MDX
export const img = ({ src, alt, ...props }: { src: string; alt: string } & Omit<ImageProps, 'src'>) => (
  <MdxImage src={src} alt={alt} {...props} />
);