import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { Post } from '@/lib/blog';

interface PostCardProps {
  post: Post;
}

export function PostCard({ post }: PostCardProps) {
  return (
    <Link href={`/blog/${post.slug}`}>
      <Card className="h-full overflow-hidden hover:shadow-lg transition-shadow duration-300 hover:scale-[1.02]">
        {post.frontmatter.featureImage && (
          <div className="relative w-full h-40 sm:h-48">
            <Image
              src={post.frontmatter.featureImage}
              alt={post.frontmatter.title}
              fill
              className="object-cover"
            />
          </div>
        )}
        <CardHeader>
          <div className="space-y-1">
            <h3 className="text-xl sm:text-2xl font-bold tracking-tight line-clamp-2">{post.frontmatter.title}</h3>
            <p className="text-xs sm:text-sm text-muted-foreground">
              {new Date(post.frontmatter.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
        </CardHeader>
        <CardContent className="py-2 sm:py-6">
          <p className="text-sm sm:text-base text-muted-foreground line-clamp-2 sm:line-clamp-3">{post.frontmatter.excerpt}</p>
        </CardContent>
        <CardFooter className="pt-2 pb-4 sm:py-6">
          <div className="flex flex-wrap gap-1.5 sm:gap-2">
            {post.frontmatter.tags?.map((tag) => (
              <Badge key={tag} variant="secondary">
                {tag}
              </Badge>
            )) ?? (
              <Badge variant="secondary">Uncategorized</Badge>
            )}
          </div>
        </CardFooter>
      </Card>
    </Link>
  );
}