"use client";

import { useMemo } from "react";
import { getMDXComponent } from "mdx-bundler/client";
import { MdxCallout } from "./MdxCallout";
import { MdxImage } from "./MdxImage";
import { Pre } from "./CodeBlock";
import { SectionDivider } from "../blog/SectionDivider";

// MDX components mapping
const mdxComponents = {
  MdxImage,
  MdxCallout,
  pre: Pre,
  SectionDivider,
};

interface MdxRendererProps {
  code: string;
}

export function MdxRenderer({ code }: MdxRendererProps) {
  const MDXContent = useMemo(() => {
    return getMDXComponent(code);
  }, [code]);

  return (
    <div className="blog-prose">
      <MDXContent components={mdxComponents} />
    </div>
  );
}
