"use client";

import { motion } from "framer-motion";
import { useAnimations } from "@/hooks/use-animations";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useState } from "react";
import { SpotifyEmbed } from "@/components/SpotifyEmbed";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function PortfolioPage() {
  const { fadeInUp, staggerChildren, cardHover, hasReducedMotion } =
    useAnimations();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Portfolio projects data
  const projects = [
    {
      id: 1,
      title: "Half Past, Jampas",
      artist: "T-Status feat. Stat",
      image: "/project1.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2025,
      audioType: "spotify",
      audioSrc: "6DIVhbOsM9Api8smo20OiG", // Spotify track ID
    },
    {
      id: 2,
      title: "Thixo",
      artist: "T-Status",
      image: "/project2.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2025,
      audioType: "spotify",
      audioSrc: "61On5W0ZtCdxz1Cori0FmC", // Spotify track ID
    },
    {
      id: 3,
      title: "Umdeni",
      artist: "T-Status",
      image: "/project3.jpg",
      categories: ["Mixing", "Mastering", "Production"],
      year: 2023,
      audioType: "spotify",
      audioSrc: "39TijP7Kas1zYPg0Px9g8U", // Spotify track ID
    },
    {
      id: 4,
      title: "Amadoda",
      artist: "KilloTronix feat. Nkosana Mkhonza & Vuvu The Drummer",
      image: "/project4.jpg",
      categories: ["Mixing", "Mastering"],
      year: 2023,
      audioType: "spotify",
      audioSrc: "4znGkNpME2P79gyc0FeLVX", // Spotify track ID
    },
  ];

  // Project detail modal state - used in the onClick handler
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedProject, setSelectedProject] = useState<
    (typeof projects)[0] | null
  >(null);

  // All unique categories
  const categories = Array.from(
    new Set(projects.flatMap((project) => project.categories))
  );

  // Filter projects by category
  const filteredProjects = selectedCategory
    ? projects.filter((project) =>
        project.categories.includes(selectedCategory)
      )
    : projects;

  return (
    <div className="container mx-auto px-4 py-16 sm:py-24">
      <motion.div
        className="text-center mb-16"
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <h1 className="text-4xl sm:text-5xl font-bold mb-4">Our Portfolio</h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Explore our recent work and the artists we&apos;ve collaborated with
        </p>
      </motion.div>

      {/* Category filters */}
      <motion.div
        className="flex flex-wrap justify-center gap-2 mb-12"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: 0.2 },
        }}
      >
        <Button
          variant={selectedCategory === null ? "default" : "outline"}
          onClick={() => setSelectedCategory(null)}
          className="mb-2"
        >
          All Projects
        </Button>
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            onClick={() => setSelectedCategory(category)}
            className="mb-2"
          >
            {category}
          </Button>
        ))}
      </motion.div>

      {/* Projects grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8"
        initial="hidden"
        animate="visible"
        variants={staggerChildren}
      >
        {filteredProjects.map((project) => (
          <motion.div
            key={project.id}
            variants={fadeInUp}
            whileHover={cardHover}
            className="h-full"
          >
            <Card className="h-full border border-white/10 bg-black/50 backdrop-blur-sm overflow-hidden">
              <div className="relative h-48 w-full overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent z-10" />
                <Image
                  src={project.image}
                  alt={`${project.title} by ${project.artist}`}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority={project.id <= 2}
                  className="object-cover"
                  style={{ position: 'absolute' }}
                />
              </div>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{project.title}</CardTitle>
                    <CardDescription>{project.artist}</CardDescription>
                  </div>
                  <Badge variant="outline">{project.year}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {project.categories.map((category) => (
                    <Badge
                      key={category}
                      variant="secondary"
                      className="text-xs"
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex flex-col gap-4">
                {project.audioType === "spotify" && (
                  <SpotifyEmbed
                    spotifyId={project.audioSrc}
                    type="track"
                    className="w-full"
                  />
                )}

                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => setSelectedProject(project)}
                    >
                      View Details
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="bg-black/90 backdrop-blur-md border-white/10 max-w-3xl">
                    <DialogHeader>
                      <DialogTitle className="text-2xl">
                        {project.title} - {project.artist}
                      </DialogTitle>
                    </DialogHeader>

                    <div className="mt-4 space-y-6">
                      {/* Project image */}
                      <div className="relative w-full h-48 rounded-md overflow-hidden">
                        <Image
                          src={project.image}
                          alt={`${project.title} by ${project.artist}`}
                          fill
                          sizes="(max-width: 1200px) 100vw, 50vw"
                          className="object-cover"
                          style={{ position: 'absolute' }}
                        />
                      </div>

                      {/* Project details */}
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-medium text-orange-500">
                            Services provided
                          </h3>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {project.categories.map((category) => (
                              <Badge key={category} variant="secondary">
                                {category}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        {/* Spotify embed */}
                        {project.audioType === "spotify" && (
                          <div>
                            <h3 className="text-lg font-medium text-orange-500 mb-2">
                              Listen
                            </h3>
                            <SpotifyEmbed
                              spotifyId={project.audioSrc}
                              type="track"
                              height={152}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardFooter>
            </Card>
          </motion.div>
        ))}
      </motion.div>

      {/* Call to action */}
      <motion.div
        className="text-center mt-20"
        initial={{ opacity: 0 }}
        animate={{
          opacity: 1,
          transition: { delay: hasReducedMotion ? 0 : 0.5 },
        }}
      >
        <h2 className="text-2xl font-bold mb-4">
          Ready to create your next project?
        </h2>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Let&apos;s collaborate to bring your musical vision to life with
          professional quality.
        </p>
        <Button size="lg" className="bg-orange-500 hover:bg-orange-600">
          <Link href="/contact">Start Your Project</Link>
        </Button>
      </motion.div>
    </div>
  );
}
