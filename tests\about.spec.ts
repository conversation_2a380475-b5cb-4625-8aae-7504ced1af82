import { test, expect } from '@playwright/test';
import { AboutPage } from './pages/AboutPage';
import { TestHelpers } from './utils/test-helpers';

test.describe('About Page', () => {
  let aboutPage: AboutPage;

  test.beforeEach(async ({ page }) => {
    aboutPage = new AboutPage(page);
    await aboutPage.goto();
  });

  test('should display page title', async () => {
    await aboutPage.expectPageTitle();
  });

  test('should display about content', async () => {
    await aboutPage.expectAboutContent();
  });

  test('should display mission statement', async () => {
    await aboutPage.expectMissionStatement();
  });

  test('should display team information', async () => {
    await aboutPage.expectTeamInformation();
  });

  test('should display company values', async () => {
    await aboutPage.expectCompanyValues();
  });

  test('should be responsive', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await aboutPage.expectResponsiveLayout();
  });

  test('should load without console errors', async ({ page }) => {
    const errors = await TestHelpers.checkConsoleErrors(page);
    expect(errors).toHaveLength(0);
  });

  test('should have proper accessibility features', async ({ page }) => {
    await TestHelpers.checkAccessibility(page);
  });

  test('should have good SEO basics', async ({ page }) => {
    await TestHelpers.checkSEO(page);
  });
});
