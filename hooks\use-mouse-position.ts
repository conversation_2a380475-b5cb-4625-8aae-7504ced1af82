"use client";

import { useState, useEffect } from "react";

export function useMousePosition() {
  const [mousePos, setMousePos] = useState({ x: 0.5, y: 0.5 });

  useEffect(() => {
    const updateMousePos = (e: MouseEvent) => {
      setMousePos({
        x: e.clientX / window.innerWidth,
        y: 1.0 - e.clientY / window.innerHeight, // Invert Y for WebGL coordinates
      });
    };

    window.addEventListener("mousemove", updateMousePos);
    return () => window.removeEventListener("mousemove", updateMousePos);
  }, []);

  return mousePos;
}
