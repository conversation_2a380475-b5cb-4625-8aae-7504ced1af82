import { test, expect } from '@playwright/test';

test.describe('Basic Application Functionality', () => {
  test('homepage loads correctly', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Check title
    await expect(page).toHaveTitle(/Noize Capital/);
    
    // Check main logo is visible
    await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
    
    // Check tagline
    await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
    
    // Check services button
    await expect(page.getByRole('link', { name: 'Our Services' })).toBeVisible();
  });

  test('navigation works across all pages', async ({ page }) => {
    await page.goto('http://localhost:3000');
    
    // Test navigation to each page
    const navItems = [
      { name: 'Services', expectedText: 'Mixing' },
      { name: 'Portfolio', expectedText: 'Our Portfolio' },
      { name: 'Blog', expectedText: 'Insights, tutorials, and guides' },
      { name: 'About', expectedText: 'About Us' },
      { name: 'Contact', expectedText: 'Contact' },
    ];

    for (const item of navItems) {
      // Click navigation item
      await page.getByRole('link', { name: item.name }).click();
      
      // Wait for page to load
      await page.waitForLoadState('networkidle');
      
      // Check that we're on the right page
      await expect(page.getByText(item.expectedText)).toBeVisible();
      
      // Go back to homepage for next iteration
      await page.getByRole('link', { name: 'Noize Capital' }).click();
      await page.waitForLoadState('networkidle');
    }
  });

  test('services page displays pricing cards', async ({ page }) => {
    await page.goto('http://localhost:3000/services');
    
    // Check for service types
    await expect(page.getByText('Mixing')).toBeVisible();
    await expect(page.getByText('Mastering')).toBeVisible();
    await expect(page.getByText('Full Package')).toBeVisible();
    
    // Check for pricing
    await expect(page.getByText('$35')).toBeVisible();
    await expect(page.getByText('$20')).toBeVisible();
    await expect(page.getByText('$50')).toBeVisible();
  });

  test('blog page displays posts', async ({ page }) => {
    await page.goto('http://localhost:3000/blog');
    
    // Check page title
    await expect(page.getByText('Insights, tutorials, and guides')).toBeVisible();
    
    // Check for actual blog post titles (from the real content)
    await expect(page.getByText('I Compared $50 AI Mastering vs $500 Professional Mastering')).toBeVisible();
    await expect(page.getByText('The Track Prep Checklist That Saved Me $2,000')).toBeVisible();
    await expect(page.getByText('The 5 Mixing Mistakes That Are Killing Your Tracks')).toBeVisible();
    
    // Check for tags
    await expect(page.getByText('Mastering')).toBeVisible();
    await expect(page.getByText('Mixing')).toBeVisible();
    await expect(page.getByText('Tips')).toBeVisible();
  });

  test('portfolio page displays projects', async ({ page }) => {
    await page.goto('http://localhost:3000/portfolio');
    
    // Check page title
    await expect(page.getByText('Our Portfolio')).toBeVisible();
    
    // Check for project (from the actual content we saw earlier)
    await expect(page.getByText('Amadoda')).toBeVisible();
    await expect(page.getByText('KilloTronix')).toBeVisible();
    
    // Check for filter buttons
    await expect(page.getByText('All')).toBeVisible();
    await expect(page.getByText('Mixing')).toBeVisible();
    await expect(page.getByText('Mastering')).toBeVisible();
  });

  test('about page displays content', async ({ page }) => {
    await page.goto('http://localhost:3000/about');
    
    // Check page title
    await expect(page.getByText('About Us')).toBeVisible();
    
    // Check for actual content from the about page
    await expect(page.getByText('Passionate about sound, dedicated to your music')).toBeVisible();
    await expect(page.getByText('Noize Capital was founded in 2020 by Melvin Mpolokeng')).toBeVisible();
    await expect(page.getByText('Melvin Mpolokeng')).toBeVisible();
    await expect(page.getByText('Founder & Lead Engineer')).toBeVisible();
  });

  test('responsive design works on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    await page.goto('http://localhost:3000');
    
    // Check that main content is still visible
    await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
    await expect(page.getByText('Where we make your ideas audible')).toBeVisible();
    
    // Test navigation on mobile (might have hamburger menu)
    const navLink = page.getByRole('link', { name: 'Services' });
    if (await navLink.isVisible()) {
      await navLink.click();
      await page.waitForLoadState('networkidle');
      await expect(page.getByText('Mixing')).toBeVisible();
    }
  });

  test('no critical console errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        const text = msg.text();
        // Filter out expected WebGL errors in headless mode
        if (!text.includes('WebGL') && 
            !text.includes('THREE.WebGLRenderer') && 
            !text.includes('canvas.getContext')) {
          errors.push(text);
        }
      }
    });

    await page.goto('http://localhost:3000');
    await page.waitForTimeout(2000);
    
    // Should have no critical errors
    expect(errors).toHaveLength(0);
  });

  test('page loads with reasonable performance', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    const loadTime = Date.now() - startTime;
    
    // Should load within 10 seconds (generous for CI)
    expect(loadTime).toBeLessThan(10000);
    
    // Check that main content is visible
    await expect(page.locator('img[alt="Noize Capital"]')).toBeVisible();
  });

  test('all pages have proper titles', async ({ page }) => {
    const pages = [
      { path: '/', expectedTitle: /Noize Capital/ },
      { path: '/services', expectedTitle: /Noize Capital/ },
      { path: '/portfolio', expectedTitle: /Noize Capital/ },
      { path: '/blog', expectedTitle: /Noize Capital/ },
      { path: '/about', expectedTitle: /Noize Capital/ },
      { path: '/contact', expectedTitle: /Noize Capital/ },
    ];

    for (const pageInfo of pages) {
      await page.goto(`http://localhost:3000${pageInfo.path}`);
      await expect(page).toHaveTitle(pageInfo.expectedTitle);
    }
  });
});
