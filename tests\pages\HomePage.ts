import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class HomePage extends BasePage {
  readonly logo: Locator;
  readonly tagline: Locator;
  readonly servicesButton: Locator;
  readonly waveformBackground: Locator;

  constructor(page: Page) {
    super(page);
    this.logo = page.locator('img[alt="Noize Capital"]');
    this.tagline = page.getByText('Where we make your ideas audible');
    this.servicesButton = page.getByRole('link', { name: 'Our Services' });
    this.waveformBackground = page.locator('[class*="waveform"]');
  }

  async goto() {
    await super.goto('/');
  }

  async expectLogoVisible() {
    await expect(this.logo).toBeVisible();
  }

  async expectTaglineVisible() {
    await expect(this.tagline).toBeVisible();
  }

  async expectServicesButtonVisible() {
    await expect(this.servicesButton).toBeVisible();
  }

  async clickServicesButton() {
    await this.servicesButton.click();
    await this.waitForPageLoad();
  }

  async expectPageContent() {
    await this.expectLogoVisible();
    await this.expectTaglineVisible();
    await this.expectServicesButtonVisible();
  }

  // Test logo animations (if not reduced motion)
  async expectLogoAnimations() {
    // Check if logo has animation classes or styles
    const logoElement = this.logo;
    await expect(logoElement).toBeVisible();
    
    // You can add more specific animation checks here
    // For example, checking CSS transforms or opacity changes
  }

  // Test responsive design
  async expectResponsiveLayout() {
    const isMobile = await this.isMobileViewport();
    
    if (isMobile) {
      // Mobile-specific expectations
      await expect(this.logo).toBeVisible();
      await expect(this.tagline).toBeVisible();
    } else {
      // Desktop-specific expectations
      await expect(this.logo).toBeVisible();
      await expect(this.tagline).toBeVisible();
      await expect(this.servicesButton).toBeVisible();
    }
  }
}
