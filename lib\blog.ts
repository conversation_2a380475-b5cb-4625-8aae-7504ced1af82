import { z } from "zod";
import { globSync } from 'glob';
import fs from 'fs';
import path from 'path';
import { bundleMDX } from 'mdx-bundler';
import remarkGfm from 'remark-gfm';
import remarkMdxImages from 'remark-mdx-images';
import rehypePrettyCode from 'rehype-pretty-code';

// Helper function to format image path
function formatImagePath(path: string): string {
  if (path.startsWith('http')) return path;
  return path.startsWith('/') ? path : `/blog/${path}`;
}

// Define the PostFrontmatter schema with Zod
export const PostFrontmatter = z.object({
  title: z.string(),
  date: z.string(),
  tags: z.array(z.enum(['Mixing', 'Mastering', 'Tips', 'Client Guides'])),
  excerpt: z.string(),
  featureImage: z.string()
    .transform(formatImagePath)
    .optional(),
  published: z.boolean().default(true),
});

export type PostFrontmatter = z.infer<typeof PostFrontmatter>;

export interface Post {
  frontmatter: PostFrontmatter;
  slug: string;
  code: string;
}

export interface Toc {
  value: string;
  depth: number;
  url: string;
}

async function processMDX(source: string) {
  const { code, frontmatter } = await bundleMDX({
    source,
    mdxOptions(options) {
      options.remarkPlugins = [
        ...(options.remarkPlugins ?? []),
        remarkGfm,
        remarkMdxImages,
      ];
      options.rehypePlugins = [
        ...(options.rehypePlugins ?? []),
        [
          rehypePrettyCode,
          {
            theme: 'github-dark',
            keepBackground: true,
          },
        ],
      ];
      return options;
    },
  });

  // Validate frontmatter
  const validatedFrontmatter = PostFrontmatter.parse(frontmatter);

  return {
    code,
    frontmatter: validatedFrontmatter,
  };
}

export async function getPosts(): Promise<Post[]> {
  const posts = await Promise.all(
    globSync('app/blog/posts/*.mdx').map(async (filePath) => {
      const source = await fs.promises.readFile(filePath, 'utf8');
      const { code, frontmatter } = await processMDX(source);
      
      return {
        frontmatter,
        slug: path.basename(filePath, '.mdx'),
        code,
      };
    })
  );

  return posts
    .filter((post) => post.frontmatter.published)
    .sort((a, b) => 
      new Date(b.frontmatter.date).getTime() - 
      new Date(a.frontmatter.date).getTime()
    );
}

export async function getPostBySlug(slug: string): Promise<Post | null> {
  try {
    // Sanitize slug to prevent directory traversal
    const sanitizedSlug = path.basename(slug);
    const filePath = `app/blog/posts/${sanitizedSlug}.mdx`;

    // Check if file exists first
    const fileExists = fs.existsSync(filePath);
    if (!fileExists) {
      return null;
    }

    const source = await fs.promises.readFile(filePath, 'utf8');
    const { code, frontmatter } = await processMDX(source);
    
    // Don't return unpublished posts
    if (!frontmatter.published) {
      return null;
    }
    
    return {
      frontmatter,
      slug: sanitizedSlug,
      code,
    };
  } catch (error) {
    console.error(`Error loading post ${slug}:`, error);
    return null;
  }
}

// Add function to get static paths
export async function getPostSlugs(): Promise<string[]> {
  const files = globSync('app/blog/posts/*.mdx');
  return files.map(file => path.basename(file, '.mdx'));
}

export function getAllTags(posts: Post[]): string[] {
  const tags = new Set<string>();
  posts.forEach((post) => {
    post.frontmatter.tags.forEach((tag) => tags.add(tag));
  });
  return Array.from(tags);
}