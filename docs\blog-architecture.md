# Blog Architecture Plan

## File Structure
```
app/
  blog/
    page.tsx            # Blog listing page
    [slug]/
      page.tsx          # Individual post page
    posts/
      post-1.mdx        # MDX content files
      post-2.mdx
components/
  blog/
    PostCard.tsx        # Grid card component
    TagsFilter.tsx      # Tag filtering UI
    TableOfContents.tsx # Post navigation
lib/
  blog.ts               # Content parsing utilities
```

## Data Model (Post Frontmatter)
```ts
interface BlogPost {
  title: string
  date: string // ISO format
  tags: Array<'Mixing' | 'Mastering' | 'Tips' | 'Client Guides'>
  excerpt: string
  featureImage?: string
  published: boolean
}
```

## Technical Implementation

### 1. MDX Configuration
- Add `@next/mdx` and `gray-matter` dependencies
- Update `next.config.ts`:
```ts
const withMDX = require('@next/mdx')({
  extension: /\.mdx?$/,
})

const nextConfig: NextConfig = {
  // ...existing config
  pageExtensions: ['ts', 'tsx', 'mdx'],
}

module.exports = withMDX(nextConfig)
```

### 2. Content Loading
- Store MDX files in `app/blog/posts`
- Create content loader in `lib/blog.ts`:
```ts
export async function getPosts() {
  return globSync('app/blog/posts/*.mdx').map(async (path) => {
    const { data, content } = matter(await fs.promises.readFile(path))
    return {
      ...data,
      slug: path.split('/').pop()?.replace('.mdx', ''),
      content
    } as BlogPost
  })
}
```

### 3. URL Structure
- Listing: `/blog`
- Posts: `/blog/{slug}`
- Tags: `/blog?tag={tag}`

### 4. Required Components
- `BlogLayout`: Main page template
- `PostCard`: Grid item with excerpt/image
- `TagsFilter`: Interactive tag selector
- `MDXComponents`: Custom MDX shortcodes
- `TableOfContents`: Auto-generated from headings

## Next Steps
1. Install required MDX dependencies
2. Create content parsing utilities
3. Develop core blog components
4. Implement tag filtering logic
5. Add sample content for testing